package roaring

import (
	"math/bits"
	"sync"

	"github.com/kelindar/bitmap"
)

// Buffer pool for reducing allocations during AND operations
var emptyContainerPool = sync.Pool{
	New: func() interface{} {
		return make([]uint16, 0, 32)
	},
}

// And performs bitwise AND operation with other bitmap(s)
func (rb *Bitmap) And(other *Bitmap, extra ...*Bitmap) {
	// Handle nil inputs efficiently
	if other == nil {
		rb.Clear()
		return
	}

	// Create list of valid bitmaps
	bitmaps := make([]*Bitmap, 0, len(extra)+1)
	bitmaps = append(bitmaps, other)
	for _, bm := range extra {
		if bm != nil && bm.count > 0 {
			bitmaps = append(bitmaps, bm)
		}
	}

	// If no valid bitmaps or empty result, clear
	if len(bitmaps) == 0 || rb.count == 0 {
		rb.Clear()
		return
	}

	// Single bitmap optimization
	if len(bitmaps) == 1 {
		rb.and<PERSON>ing<PERSON>(bitmaps[0])
		return
	}

	// Multiple bitmaps - use iterative approach
	for _, bm := range bitmaps {
		if rb.count == 0 {
			break // Early exit
		}
		rb.andSingle(bm)
	}
}

// and<PERSON><PERSON><PERSON> performs AND with a single bitmap efficiently
func (rb *Bitmap) andSingle(other *Bitmap) {
	if other.count == 0 {
		rb.Clear()
		return
	}

	// Find intersection of container ranges using span optimization
	minStart := max(rb.span[0], other.span[0])
	minEnd := min(rb.span[1], other.span[1])

	if minStart > minEnd {
		rb.Clear()
		return
	}

	// Track containers that become empty for batch removal
	emptyContainers := emptyContainerPool.Get().([]uint16)
	emptyContainers = emptyContainers[:0] // Reset length but keep capacity
	defer emptyContainerPool.Put(emptyContainers)

	// Process only intersecting containers
	for i := int(minStart); i <= int(minEnd); i++ {
		block := rb.blocks[i]
		otherBlock := other.blocks[i]

		if block != nil || otherBlock != nil {
		}

		if block == nil || otherBlock == nil {
			// No intersection at this block level
			if block != nil {
				for j := int(block.span[0]); j <= int(block.span[1]); j++ {
					if c := block.content[j]; c != nil {
						emptyContainers = append(emptyContainers, c.Key)
					}
				}
			}
			continue
		}

		// Find intersection within blocks
		blockMinStart := max(block.span[0], otherBlock.span[0])
		blockMinEnd := min(block.span[1], otherBlock.span[1])

		if blockMinStart > blockMinEnd {
			// No intersection at container level - remove entire block
			for j := int(block.span[0]); j <= int(block.span[1]); j++ {
				if c := block.content[j]; c != nil {
					emptyContainers = append(emptyContainers, c.Key)
				}
			}
			continue
		}

		// Process containers within intersecting range
		for j := int(blockMinStart); j <= int(blockMinEnd); j++ {
			c1 := block.content[j]
			c2 := otherBlock.content[j]

			if c1 != nil || c2 != nil {
			}

			if c1 == nil || c2 == nil {
				if c1 != nil {
					emptyContainers = append(emptyContainers, c1.Key)
				}
				continue
			}

			// Perform container AND
			if !rb.andContainers(c1, c2) {
				emptyContainers = append(emptyContainers, c1.Key)
			}
		}

		// Remove containers outside intersection range
		for j := int(block.span[0]); j < int(blockMinStart); j++ {
			if c := block.content[j]; c != nil {
				emptyContainers = append(emptyContainers, c.Key)
			}
		}
		for j := int(blockMinEnd) + 1; j <= int(block.span[1]); j++ {
			if c := block.content[j]; c != nil {
				emptyContainers = append(emptyContainers, c.Key)
			}
		}
	}

	// Remove containers outside intersection range
	for i := int(rb.span[0]); i < int(minStart); i++ {
		if block := rb.blocks[i]; block != nil {
			for j := int(block.span[0]); j <= int(block.span[1]); j++ {
				if c := block.content[j]; c != nil {
					emptyContainers = append(emptyContainers, c.Key)
				}
			}
		}
	}
	for i := int(minEnd) + 1; i <= int(rb.span[1]); i++ {
		if block := rb.blocks[i]; block != nil {
			for j := int(block.span[0]); j <= int(block.span[1]); j++ {
				if c := block.content[j]; c != nil {
					emptyContainers = append(emptyContainers, c.Key)
				}
			}
		}
	}

	// Batch remove empty containers
	for _, hi := range emptyContainers {
		rb.removeContainer(hi)
	}
}

// andContainers performs efficient AND between two containers
func (rb *Bitmap) andContainers(c1, c2 *container) bool {
	// Ensure we own c1's data before modifying it (COW protection)
	c1.cowEnsureOwned()

	// Use most efficient algorithm based on container types
	switch {
	case c1.Type == typeArray && c2.Type == typeArray:
		return rb.andArrayArray(c1, c2)
	case c1.Type == typeArray && c2.Type == typeBitmap:
		return rb.andArrayBitmap(c1, c2)
	case c1.Type == typeBitmap && c2.Type == typeArray:
		return rb.andBitmapArray(c1, c2)
	case c1.Type == typeBitmap && c2.Type == typeBitmap:
		return rb.andBitmapBitmap(c1, c2)
	case c1.Type == typeRun:
		return rb.andRunContainer(c1, c2)
	case c2.Type == typeRun:
		return rb.andContainerRun(c1, c2)
	default:
		return false
	}
}

// andArrayArray performs AND between two array containers
func (rb *Bitmap) andArrayArray(c1, c2 *container) bool {
	arr1 := c1.arr()
	arr2 := c2.arr()
	if len(arr1) == 0 || len(arr2) == 0 {
		return false
	}

	out := arr1[:0]
	i, j := 0, 0
	for i < len(arr1) && j < len(arr2) {
		switch {
		case arr1[i] == arr2[j]:
			out = append(out, arr1[i])
			i++
			j++
		case arr1[i] < arr2[j]:
			i++
		case arr1[i] > arr2[j]:
			j++
		}
	}

	c1.Data = out
	c1.Size = uint32(len(out))
	return true
}

// andArrayBitmap performs AND between array and bitmap containers
func (rb *Bitmap) andArrayBitmap(c1, c2 *container) bool {
	arr := c1.arr()
	bmp := c2.bmp()
	result := arr[:0]

	for _, val := range arr {
		if bmp.Contains(uint32(val)) {
			result = append(result, val)
		}
	}

	if len(result) == 0 {
		return false
	}

	c1.Data = result
	c1.Size = uint32(len(result))
	return true
}

// andBitmapArray performs AND between bitmap and array containers
func (rb *Bitmap) andBitmapArray(c1, c2 *container) bool {
	bmp := c1.bmp()
	arr := c2.arr()

	// Create result array directly - more efficient than bitmap manipulation
	result := make([]uint16, 0, len(arr))

	for _, val := range arr {
		if bmp.Contains(uint32(val)) {
			result = append(result, val)
		}
	}

	if len(result) == 0 {
		return false
	}

	// Convert container to array type with the result
	c1.Data = result
	c1.Type = typeArray
	c1.Size = uint32(len(result))

	return true
}

// andBitmapBitmap performs AND between two bitmap containers
func (rb *Bitmap) andBitmapBitmap(c1, c2 *container) bool {
	bmp1 := c1.bmp()
	bmp2 := c2.bmp()
	if bmp1 == nil || bmp2 == nil {
		return false
	}

	// Use the bitmap library's And operation for efficiency
	bmp1.And(bmp2)

	// Count the result
	count := bmp1.Count()
	if count == 0 {
		return false
	}

	c1.Size = uint32(count)

	// Convert to array if small enough
	if count <= arrMinSize {
		rb.bitmapToArray(c1)
	}

	return true
}

// andRunContainer performs AND between run container and other container
func (rb *Bitmap) andRunContainer(c1, c2 *container) bool {
	runs := c1.run()

	// Pre-allocate with reasonable capacity to reduce reallocations
	newRuns := make([]run, 0, len(runs))

	// Use efficient algorithms based on c2's type
	switch c2.Type {
	case typeArray:
		newRuns = rb.andRunArray(runs, c2.arr(), newRuns)
	case typeBitmap:
		newRuns = rb.andRunBitmap(runs, c2.bmp(), newRuns)
	case typeRun:
		newRuns = rb.andRunRun(runs, c2.run(), newRuns)
	default:
		return false
	}

	if len(newRuns) == 0 {
		return false
	}

	// Reuse existing data slice if it's large enough
	requiredSize := len(newRuns) * 2
	if cap(c1.Data) >= requiredSize {
		c1.Data = c1.Data[:requiredSize]
	} else {
		c1.Data = make([]uint16, requiredSize)
	}

	newRunsSlice := c1.run()
	copy(newRunsSlice, newRuns)

	// Recalculate size
	c1.Size = 0
	for _, r := range newRuns {
		c1.Size += uint32(r[1] - r[0] + 1)
	}

	return true
}

// andRunArray performs efficient AND between run container and array container
func (rb *Bitmap) andRunArray(runs []run, arr []uint16, result []run) []run {
	if len(runs) == 0 || len(arr) == 0 {
		return result[:0]
	}

	arrIdx := 0
	for _, r := range runs {
		start, end := r[0], r[1]

		// Skip array elements before this run
		for arrIdx < len(arr) && arr[arrIdx] < start {
			arrIdx++
		}

		// Find intersecting elements in this run
		currentStart := uint16(0)
		inRun := false

		for arrIdx < len(arr) && arr[arrIdx] <= end {
			val := arr[arrIdx]
			if !inRun {
				currentStart = val
				inRun = true
			}

			// Check if next element is consecutive
			if arrIdx+1 >= len(arr) || arr[arrIdx+1] != val+1 || arr[arrIdx+1] > end {
				// End of consecutive sequence
				result = append(result, run{currentStart, val})
				inRun = false
			}
			arrIdx++
		}
	}

	return result
}

// andRunBitmap performs efficient AND between run container and bitmap container
func (rb *Bitmap) andRunBitmap(runs []run, bmp bitmap.Bitmap, result []run) []run {
	if len(runs) == 0 || bmp == nil {
		return result[:0]
	}

	for _, r := range runs {
		start, end := r[0], r[1]

		currentStart := uint16(0)
		inRun := false

		for val := start; val <= end; val++ {
			if bmp.Contains(uint32(val)) {
				if !inRun {
					currentStart = val
					inRun = true
				}
			} else if inRun {
				result = append(result, run{currentStart, val - 1})
				inRun = false
			}

			if val == end {
				break // Prevent overflow
			}
		}

		// Handle final run
		if inRun {
			result = append(result, run{currentStart, end})
		}
	}

	return result
}

// andRunRun performs efficient AND between two run containers
func (rb *Bitmap) andRunRun(runs1, runs2 []run, result []run) []run {
	if len(runs1) == 0 || len(runs2) == 0 {
		return result[:0]
	}

	i, j := 0, 0
	for i < len(runs1) && j < len(runs2) {
		r1, r2 := runs1[i], runs2[j]

		// Find intersection of the two runs
		start := max(r1[0], r2[0])
		end := min(r1[1], r2[1])

		if start <= end {
			// There's an intersection
			result = append(result, run{start, end})
		}

		// Advance the run that ends first
		if r1[1] <= r2[1] {
			i++
		} else {
			j++
		}
	}

	return result
}

// andContainerRun performs AND between container and run container
func (rb *Bitmap) andContainerRun(c1, c2 *container) bool {
	runs := c2.run()

	switch c1.Type {
	case typeArray:
		arr := c1.arr()
		result := arr[:0]

		// Use binary search approach for better performance
		runIdx := 0
		for _, val := range arr {
			// Skip runs that end before this value
			for runIdx < len(runs) && runs[runIdx][1] < val {
				runIdx++
			}

			// Check if value is in current or later runs
			for i := runIdx; i < len(runs); i++ {
				r := runs[i]
				if val < r[0] {
					break // Value is before this run, won't be in any later runs
				}
				if val <= r[1] {
					result = append(result, val)
					break
				}
			}
		}

		if len(result) == 0 {
			return false
		}

		c1.Data = result
		c1.Size = uint32(len(result))
		return true

	case typeBitmap:
		bmp := c1.bmp()
		count := 0

		// Process runs efficiently without copying entire bitmap
		for _, r := range runs {
			start, end := r[0], r[1]

			// Process word by word for better performance
			startBlk := start >> 6
			endBlk := end >> 6

			if startBlk == endBlk {
				// Run is within single 64-bit word
				blkIdx := int(startBlk)
				if blkIdx < len(bmp) {
					startBit := start & 63
					endBit := end & 63
					mask := (uint64(1)<<(endBit-startBit+1) - 1) << startBit
					word := bmp[blkIdx] & mask
					if word != 0 {
						// Clear other bits in this word for this run
						bmp[blkIdx] = (bmp[blkIdx] &^ mask) | word
						count += bits.OnesCount64(word)
					} else {
						// Clear the bits for this run
						bmp[blkIdx] &^= mask
					}
				}
			} else {
				// Run spans multiple words - process each word
				for blkIdx := int(startBlk); blkIdx <= int(endBlk) && blkIdx < len(bmp); blkIdx++ {
					var mask uint64 = ^uint64(0) // All bits set

					if blkIdx == int(startBlk) {
						// First word - mask from start bit to end
						startBit := start & 63
						mask = ^uint64(0) << startBit
					}
					if blkIdx == int(endBlk) {
						// Last word - mask from start to end bit
						endBit := end & 63
						mask &= (uint64(1)<<(endBit+1) - 1)
					}

					word := bmp[blkIdx] & mask
					if word != 0 {
						count += bits.OnesCount64(word)
					}
					// Keep only the intersecting bits
					bmp[blkIdx] = (bmp[blkIdx] &^ mask) | word
				}
			}
		}

		if count == 0 {
			return false
		}

		c1.Size = uint32(count)
		return true
	}

	return false
}

// bitmapToArray converts bitmap container to array if efficient
func (rb *Bitmap) bitmapToArray(c *container) {
	if c.Type != typeBitmap || c.Size > arrMinSize {
		return
	}

	// Ensure we own the data before modifying (COW protection)
	c.cowEnsureOwned()

	bmp := c.bmp()
	arr := make([]uint16, 0, c.Size)

	// Use the bitmap's Range method for efficient iteration
	bmp.Range(func(value uint32) {
		arr = append(arr, uint16(value))
	})

	c.Data = arr
	c.Type = typeArray
}

// AndNot performs bitwise AND NOT operation with other bitmap(s)
func (rb *Bitmap) AndNot(other *Bitmap, extra ...*Bitmap) {
	panic("not implemented")
}

// Or performs bitwise OR operation with other bitmap(s)
func (rb *Bitmap) Or(other *Bitmap, extra ...*Bitmap) {
	panic("not implemented")
}

// Xor performs bitwise XOR operation with other bitmap(s)
func (rb *Bitmap) Xor(other *Bitmap, extra ...*Bitmap) {
	panic("not implemented")
}
