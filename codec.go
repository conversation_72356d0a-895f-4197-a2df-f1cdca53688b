package roaring

import "io"

// ToBytes converts the bitmap to a byte slice
func (rb *Bitmap) ToBytes() []byte {
	panic("not implemented")
}

// WriteT<PERSON> writes the bitmap to a writer
func (rb *Bitmap) WriteTo(w io.Writer) (int64, error) {
	panic("not implemented")
}

// ReadFrom reads the bitmap from a reader
func (rb *Bitmap) ReadFrom(r io.Reader) (int64, error) {
	panic("not implemented")
}

// FromBytes creates a roaring bitmap from a byte buffer
func FromBytes(buffer []byte) *Bitmap {
	panic("not implemented")
}

// ReadFrom reads a roaring bitmap from an io.Reader
func ReadFrom(r io.Reader) (*Bitmap, error) {
	panic("not implemented")
}
