package roaring

import (
	"fmt"
	"math/rand/v2"
	"runtime"
	"testing"
	"time"
)

func TestDebugAndAllocations(t *testing.T) {
	// Create dense data like the benchmark
	data := make([]uint32, 1000)
	for i := 0; i < 1000; i++ {
		data[i] = uint32(rand.IntN(100)) // Dense in range [0, 100)
	}

	// Create two bitmaps with 50% of values set (like random() function)
	rb1 := New()
	rb2 := New()
	for _, v := range data {
		if rand.IntN(2) == 0 {
			rb1.Set(v)
		}
		if rand.IntN(2) == 0 {
			rb2.Set(v)
		}
	}

	fmt.Printf("rb1 count: %d, rb2 count: %d\n", rb1.Count(), rb2.Count())

	// Check container types
	for i := 0; i < 256; i++ {
		if rb1.blocks[i] != nil {
			for j := 0; j < 256; j++ {
				if c := rb1.blocks[i].content[j]; c != nil {
					fmt.Printf("rb1 container[%d][%d]: type=%d, size=%d\n", i, j, c.Type, c.Size)
				}
			}
		}
	}

	// Measure memory before AND
	var m1, m2 runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&m1)

	// Simulate the benchmark loop to see where allocations come from
	iterations := 0
	start := time.Now()
	for time.Since(start) < 100*time.Millisecond { // Short duration for testing
		clone1 := rb1.Clone(nil)
		clone2 := rb2.Clone(nil)
		clone1.And(clone2)
		iterations++
	}

	runtime.GC()
	runtime.ReadMemStats(&m2)

	fmt.Printf("Iterations: %d\n", iterations)
	fmt.Printf("Total memory allocated: %d bytes\n", m2.TotalAlloc-m1.TotalAlloc)
	fmt.Printf("Memory per iteration: %d bytes\n", (m2.TotalAlloc-m1.TotalAlloc)/uint64(iterations))

	// Test just cloning without AND to see clone overhead
	runtime.GC()
	runtime.ReadMemStats(&m1)

	for i := 0; i < 1000; i++ {
		clone1 := rb1.Clone(nil)
		clone2 := rb2.Clone(nil)
		_ = clone1
		_ = clone2
	}

	runtime.GC()
	runtime.ReadMemStats(&m2)

	fmt.Printf("Clone-only memory per iteration: %d bytes\n", (m2.TotalAlloc-m1.TotalAlloc)/1000)
}
